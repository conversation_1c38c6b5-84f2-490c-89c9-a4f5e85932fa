# Backup de Amazon Managed Service for Prometheus

**Fecha del backup:** 10 de Julio, 2025  
**Workspace original:** `ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf`  
**Alias:** `platform-metrics-store-prod`  
**Cuenta AWS:** `533267304535` (perfil ccb)  
**Región:** `us-east-1`

## 📋 Contenido del Backup

### Workspace Principal
- **Archivo:** `workspace-info.json`
- **Descripción:** Configuración completa del workspace incluyendo tags, ARN, endpoint

### Reglas de Monitoreo (10 namespaces)

| Namespace | Archivo | Descripción |
|-----------|---------|-------------|
| `kubecost-prod` | `rules-kubecost-prod.json` | Reglas de monitoreo de costos |
| `platform-monitors-ai-core-api-agents` | `rules-platform-monitors-ai-core-api-agents.json` | Monitoreo API de agentes AI |
| `platform-monitors-ai-core-rag-api` | `rules-platform-monitors-ai-core-rag-api.json` | Monitoreo API RAG |
| `platform-monitors-ai-core-rag-mcp` | `rules-platform-monitors-ai-core-rag-mcp.json` | Monitoreo MCP RAG |
| `platform-monitors-global` | `rules-platform-monitors-global.json` | Reglas globales de monitoreo |
| `platform-monitors-licenses-be` | `rules-platform-monitors-licenses-be.json` | Monitoreo backend de licencias |
| `platform-monitors-n5-calma-hub-be` | `rules-platform-monitors-n5-calma-hub-be.json` | Monitoreo Calma Hub backend |
| `platform-monitors-n5-settings-be` | `rules-platform-monitors-n5-settings-be.json` | Monitoreo Settings backend |
| `platform-monitors-orchestrator-be` | `rules-platform-monitors-orchestrator-be.json` | Monitoreo Orchestrator backend |
| `platform-monitors-users-management-be` | `rules-platform-monitors-users-management-be.json` | Monitoreo User Management backend |

## 🚀 Cómo Restaurar

### Opción 1: Crear nuevo workspace completo
```bash
./restore-prometheus.sh create
```

### Opción 2: Solo restaurar reglas en workspace existente
```bash
export NEW_WORKSPACE_ID=ws-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
./restore-prometheus.sh rules-only
```

## 💰 Información de Costos

**Workspace original:**
- Creado: 7 de Noviembre, 2024
- Tiempo activo: ~8 meses
- 10 namespaces de reglas activos
- Tags: Owner=Platform, Env=prod, cost-center=CTO

**Componentes que generan costo:**
1. **Ingestion:** Métricas enviadas al workspace
2. **Storage:** Retención de métricas históricas  
3. **Query:** Consultas realizadas (Grafana, alertas, etc.)
4. **Rules:** Evaluación de reglas de alertas y recording rules

## 🔧 Comandos Útiles

### Ver métricas de uso actual:
```bash
# Listar workspaces
aws amp list-workspaces --region us-east-1 --profile ccb

# Ver detalles del workspace
aws amp describe-workspace --workspace-id ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf --region us-east-1 --profile ccb

# Listar reglas
aws amp list-rule-groups-namespaces --workspace-id ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf --region us-east-1 --profile ccb
```

### Eliminar workspace (para ahorrar costos):
```bash
aws amp delete-workspace --workspace-id ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf --region us-east-1 --profile ccb
```

## ⚠️ Notas Importantes

1. **Alert Manager:** No se encontró configuración de Alert Manager en el workspace original
2. **Datos históricos:** Este backup NO incluye las métricas históricas, solo la configuración
3. **Dependencias:** Asegúrate de que los servicios que envían métricas estén configurados para el nuevo endpoint
4. **Grafana:** Actualizar las datasources en Grafana para apuntar al nuevo workspace

## 📊 Análisis de Costos

Para reducir costos considera:
- Reducir retención de métricas
- Optimizar reglas de recording (pre-agregación)
- Revisar frecuencia de scraping
- Eliminar métricas no utilizadas
- Usar métricas de alta cardinalidad solo cuando sea necesario
