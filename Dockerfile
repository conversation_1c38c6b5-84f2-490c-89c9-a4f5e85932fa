FROM public.ecr.aws/lambda/python:3.12 as build

# Install deps in a venv to shrink final image
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

COPY requirements.txt .
RUN pip install --upgrade pip && pip install -r requirements.txt

# ───────── final ──────────
FROM public.ecr.aws/amazonlinux/amazonlinux:2023
ENV PYTHONUNBUFFERED=1
COPY --from=build /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

WORKDIR /app
COPY src/ ./src
COPY infrastructure/ ./infrastructure
COPY *.md ./
CMD ["python", "-m", "src.main"]
