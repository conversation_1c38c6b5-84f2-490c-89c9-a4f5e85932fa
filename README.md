# Usage Tracker Service

![python](https://img.shields.io/badge/Python-3.12-blue?logo=python)

A lightweight, serverless‑friendly **SQS → DynamoDB** ingestion service. It reads usage events from an Amazon SQS queue, writes them in batches to DynamoDB, and emits basic metrics to CloudWatch. The container is optimised for run locally or in any orchestrator that supports Docker.

---

\## Table of Contents

1. [Architecture overview](#architecture-overview)
2. [Features](#features)
3. [Quick start](#quick-start)
4. [Configuration](#configuration)
5. [Local development](#local-development)
6. [Deployment (CDK & Fargate)](#deployment)
7. [Observability](#observability)
8. [Data model](#data-model)
9. [Resilience  🛡️](#resilience)
10. [Cost profile](#cost-profile)
11. [Contributing](#contributing)
12. [License](#license)

---

## Architecture overview


* **Pull model** – long‑polling with adaptive retries.
* **Batching** – up to 25 writes/round‑trip.
* **Idempotency** – `ConditionExpression attribute_not_exists(PK)` in DynamoDB.
* **Back‑pressure** – queue scales, service auto‑scales on depth.

---

## Features

* ⏱ **Low latency**, high throughput (pooled HTTP connections).
* 📦 **Small image** – multi‑stage build (\~90 MB unpacked).
* 🔒 **Least privilege**: minimal IAM policies; cross‑account supported via queue policy.
* 🩺 **Health endpoints** (`/healthz`, `/readyz`) for orchestration probes.
* 📈 **Custom metrics**: CloudWatch namespace `UsageTrackerSvc`.
* 🗄 **Clean architecture** (domain / application / infra / presentation).

---

## Quick start

### Prerequisites

| Tool       | Version     | Notes                     |
| ---------- | ----------- | ------------------------- |
| Docker     | ≥ 24        | Build & run containers    |
| AWS CLI v2 | ≥ 2.14      | For SSO & tests           |
| Python     | 3.11 / 3.12 | For local runs (optional) |

### 1 · Log in (SSO)

```bash
aws sso login --profile my-profile
export AWS_PROFILE=my-profile
```

### 2 · Run in Docker

```bash
export AWS_REGION=us-east-1
export SQS_URL=sqsQueue
export DDB_TABLE=usage_records_Table

docker build -t usage-tracker .
docker run --rm -it \
  -e AWS_REGION -e AWS_PROFILE -e SQS_URL -e DDB_TABLE \
  -p 8080:8080 \
  usage-tracker
```

Visit `http://localhost:8080/healthz` → `{ "status": "ok" }`.

### 3 · Send a test message

```bash
aws sqs send-message \
  --queue-url "$SQS_URL" \
  --message-body file://samples/usage_event.json
```

Check DynamoDB → `usage_records` for the new item.

---

## Configuration

All configuration is environment‑driven (twelve‑factor‑style).

| Variable            | Required | Default           | Description                           |
| ------------------- | -------- | ----------------- | ------------------------------------- |
| `SQS_URL`           | ✅        | –                 | Fully‑qualified SQS queue URL         |
| `DDB_TABLE`         | ✅        | –                 | DynamoDB table name                   |
| `AWS_REGION`        | ❌        | `us-east-1`       | AWS region for all clients            |
| `AWS_PROFILE`       | ❌        | –                 | SSO / shared‑config profile           |
| `SQS_WAIT_SECONDS`  | ❌        | `20`              | Long‑poll duration                    |
| `SQS_MAX_MESSAGES`  | ❌        | `10`              | ReceiveMessage batch size (SQS limit) |
| `DDB_BATCH_SIZE`    | ❌        | `25`              | DynamoDB write batch size             |
| `METRICS_NAMESPACE` | ❌        | `UsageTrackerSvc` | CloudWatch namespace                  |
| `LOG_LEVEL`         | ❌        | `INFO`            | Python log level                      |

---

## Local development

```bash
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
export SQS_URL=... DDB_TABLE=...
python -m src.main
```

* **Tests**: `pytest -q` (moto stubbed AWS).
* **Code lint**: `ruff check src/`.

---

## Observability

| Channel     | What                             | Where                                    |
| ----------- | -------------------------------- | ---------------------------------------- |
| **Logging** | Python structured JSON           | CloudWatch Logs `/aws/ecs/usage-tracker` |
| **Metrics** | `MessagesIngested` (Count)       | CloudWatch → `UsageTrackerSvc`           |
| **Alarms**  | DLQ > 0, Lambda errors (if used) | CDK autogenerates                        |

---

## Data model

| Attribute       | Key   | Purpose                                 |
| --------------- | ----- | --------------------------------------- |
| `PK` (S)        | HASH  | `MSG#<uuid>` – uniqueness & idempotency |
| `SK` (S)        | RANGE | `TYPE#usage-tracker`                    |
| `tenant_id` (S) | —     | Multi‑tenant querying (GSI 1)           |
| `timestamp` (N) | —     | Sort key on GSI 1                       |
| `payload` (M)   | —     | Raw message for audit                   |
| `ttl` (N)       | —     | 30‑day expiry (DynamoDB TTL)            |

GSI 1 → `(tenant_id, timestamp)` allows “get last N usage events per tenant”.

---

## Resilience  🛡️  <a name="resilience"></a>

* **Dead‑letter queue** – configurable via queue Redrive.
* **Retry of unprocessed items** – exponential back‑off inside `usage_repository`.
* **Graceful shutdown** – SIGTERM triggers buffer flush & thread join.