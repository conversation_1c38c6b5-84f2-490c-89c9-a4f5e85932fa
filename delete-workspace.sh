#!/bin/bash

# Script para eliminar el workspace de Prometheus y ahorrar costos
# ⚠️  CUIDADO: Esta acción es IRREVERSIBLE
# ⚠️  Se perderán TODAS las métricas históricas

WORKSPACE_ID="ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf"
REGION="us-east-1"
PROFILE="ccb"

echo "=== ELIMINAR WORKSPACE DE PROMETHEUS ==="
echo "⚠️  ADVERTENCIA: Esta acción es IRREVERSIBLE"
echo "⚠️  Se perderán TODAS las métricas históricas"
echo ""
echo "Workspace a eliminar:"
echo "  ID: $WORKSPACE_ID"
echo "  Alias: platform-metrics-store-prod"
echo "  Región: $REGION"
echo ""

read -p "¿Estás seguro de que quieres eliminar este workspace? (escribe 'DELETE' para confirmar): " confirmation

if [ "$confirmation" = "DELETE" ]; then
    echo ""
    echo "Eliminando workspace..."
    
    # Primero eliminar todas las reglas
    echo "1. Eliminando reglas de monitoreo..."
    for namespace in "kubecost-prod" "platform-monitors-ai-core-api-agents" "platform-monitors-ai-core-rag-api" "platform-monitors-ai-core-rag-mcp" "platform-monitors-global" "platform-monitors-licenses-be" "platform-monitors-n5-calma-hub-be" "platform-monitors-n5-settings-be" "platform-monitors-orchestrator-be" "platform-monitors-users-management-be"; do
        echo "  Eliminando namespace: $namespace"
        aws amp delete-rule-groups-namespace \
            --workspace-id $WORKSPACE_ID \
            --name "$namespace" \
            --region $REGION \
            --profile $PROFILE 2>/dev/null || echo "    (ya eliminado o no existe)"
    done
    
    echo ""
    echo "2. Eliminando workspace..."
    aws amp delete-workspace \
        --workspace-id $WORKSPACE_ID \
        --region $REGION \
        --profile $PROFILE
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ Workspace eliminado exitosamente"
        echo "💰 Los costos de Prometheus Managed se detendrán"
        echo ""
        echo "📝 Recuerda:"
        echo "  - Actualizar datasources en Grafana"
        echo "  - Reconfigurar servicios que envían métricas"
        echo "  - El backup está disponible en este directorio"
    else
        echo ""
        echo "❌ Error al eliminar el workspace"
        echo "Verifica los permisos y el estado del workspace"
    fi
else
    echo ""
    echo "❌ Eliminación cancelada"
    echo "El workspace permanece activo"
fi
