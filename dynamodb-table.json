{"TableName": "n5-usage-tracking-table", "AttributeDefinitions": [{"AttributeName": "tenant_id", "AttributeType": "S"}, {"AttributeName": "timestamp_message_id", "AttributeType": "S"}, {"AttributeName": "message_id", "AttributeType": "S"}, {"AttributeName": "conversation_id", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "tenant_id", "KeyType": "HASH"}, {"AttributeName": "timestamp_message_id", "KeyType": "RANGE"}], "GlobalSecondaryIndexes": [{"IndexName": "MessageIdIndex", "KeySchema": [{"AttributeName": "message_id", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "ConversationIndex", "KeySchema": [{"AttributeName": "conversation_id", "KeyType": "HASH"}, {"AttributeName": "timestamp_message_id", "KeyType": "RANGE"}], "Projection": {"ProjectionType": "ALL"}}], "BillingMode": "PAY_PER_REQUEST", "TimeToLiveSpecification": {"AttributeName": "ttl", "Enabled": true}}