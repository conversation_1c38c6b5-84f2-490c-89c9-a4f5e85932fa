{"ruleGroupsNamespace": {"arn": "arn:aws:aps:us-east-1:533267304535:rulegroupsnamespace/ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf/platform-monitors-ai-core-rag-api", "createdAt": "2025-04-29T12:56:04.662000-03:00", "data": "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", "modifiedAt": "2025-06-26T18:16:29.720000-03:00", "name": "platform-monitors-ai-core-rag-api", "status": {"statusCode": "ACTIVE", "statusReason": ""}, "tags": {"services.k8s.aws/controller-version": "prometheusservice-1.2.18", "services.k8s.aws/namespace": "ccb-pan"}}}