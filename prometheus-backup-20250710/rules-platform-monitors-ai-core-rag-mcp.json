{"ruleGroupsNamespace": {"arn": "arn:aws:aps:us-east-1:533267304535:rulegroupsnamespace/ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf/platform-monitors-ai-core-rag-mcp", "createdAt": "2025-05-05T15:39:25.839000-03:00", "data": "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", "modifiedAt": "2025-06-23T17:00:17.058000-03:00", "name": "platform-monitors-ai-core-rag-mcp", "status": {"statusCode": "ACTIVE", "statusReason": ""}, "tags": {"services.k8s.aws/controller-version": "prometheusservice-1.2.18", "services.k8s.aws/namespace": "ccb-pan"}}}