{"ruleGroupsNamespace": {"arn": "arn:aws:aps:us-east-1:533267304535:rulegroupsnamespace/ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf/platform-monitors-global", "createdAt": "2025-02-11T12:41:06.698000-03:00", "data": "Z3JvdXBzOgotIG5hbWU6IE5ldHdvcmtpbmcKICBydWxlczoKICAtIGFsZXJ0OiBDb3JlRE5TTGF0ZW5jeUhpZ2gKICAgIGZvcjogNW0KICAgIGV4cHI6IGhpc3RvZ3JhbV9xdWFudGlsZSgwLjk1LCBzdW0ocmF0ZShjb3JlZG5zX2Ruc19yZXF1ZXN0X2R1cmF0aW9uX3NlY29uZHNfYnVja2V0e2luc3RhbmNlPX4iLioifVsybV0pKSBieSAoc2VydmVyLHpvbmUsbGUsaW5zdGFuY2UpKSA+IDAuMDEKICAgIGxhYmVsczoKICAgICAgc2V2ZXJpdHk6IGVycm9yCiAgICAgIGV2ZW50X3R5cGU6IGF2YWlsYWJpbGl0eQogICAgICB0ZWFtOiBwbGF0Zm9ybQogICAgICBlbnY6IGRldmVsb3AKICAgICAgdmVyc2lvbjogbWFpbgogICAgICBzZXJ2aWNlOiBjb3JlZG5zCiAgICAgIHRlbmFudE5hbWU6IGRldmVsb3AKICAgICAgdGVuYW50Q291bnRyeTogZ2xvYmFsCiAgICBhbm5vdGF0aW9uczoKICAgICAgc3VtbWFyeTogQ29yZUROUyBQOTUgbGF0ZW5jeSBoYXMgZXNjYWxhdGVkCiAgICAgIGRlc2NyaXB0aW9uOiBMYXRlbmN5IGlzIHN1c3RhaW5lZCBhYm92ZSBhIGhpZ2ggd2F0ZXJtYXJrIGZvciBhIHdoaWxlCgogIC0gYWxlcnQ6IEV4dGVybmFsRE5TRXJyb3JzCiAgICBmb3I6IDVtCiAgICBleHByOiBpbmNyZWFzZShleHRlcm5hbF9kbnNfcmVnaXN0cnlfZXJyb3JzX3RvdGFsWzJtXSkgPiAwCiAgICBsYWJlbHM6CiAgICAgIHNldmVyaXR5OiBlcnJvcgogICAgICBldmVudF90eXBlOiBhdmFpbGFiaWxpdHkKICAgICAgdGVhbTogcGxhdGZvcm0KICAgICAgZW52OiBkZXZlbG9wCiAgICAgIHZlcnNpb246IG1haW4KICAgICAgc2VydmljZTogZXh0ZXJuYWwtZG5zCiAgICAgIHRlbmFudE5hbWU6IGRldmVsb3AKICAgICAgdGVuYW50Q291bnRyeTogZ2xvYmFsCiAgICBhbm5vdGF0aW9uczoKICAgICAgc3VtbWFyeTogRXh0ZXJuYWxETlMgaXMgaGF2aW5nIGVycm9ycwogICAgICBkZXNjcmlwdGlvbjogaWYgdGhlcmUgYXJlIGluZnJhIGNoYW5nZXMsIHJlY29yZHMgbWlnaHQgYmUgb3V0IG9mIGRhdGUuCgotIG5hbWU6IENlcnRpZmljYXRlcwogIHJ1bGVzOgogIC0gYWxlcnQ6IEV4cGlyYXRpb24KICAgIGZvcjogNW0KICAgIGV4cHI6IHNvcnRfZGVzYyggc3VtKGNlcnRtYW5hZ2VyX2NlcnRpZmljYXRlX2V4cGlyYXRpb25fdGltZXN0YW1wX3NlY29uZHMgLSB0aW1lKCkpIGJ5IChuYW1lLGV4cG9ydGVkX25hbWVzcGFjZSkgKSA8IDEyOTYwMDAKICAgIGxhYmVsczoKICAgICAgc2V2ZXJpdHk6IGVycm9yCiAgICAgIGV2ZW50X3R5cGU6IGF2YWlsYWJpbGl0eQogICAgICB0ZWFtOiBwbGF0Zm9ybQogICAgICBlbnY6IGRldmVsb3AKICAgICAgdmVyc2lvbjogbWFpbgogICAgICBzZXJ2aWNlOiBjZXJ0bWFuYWdlcgogICAgICB0ZW5hbnROYW1lOiBkZXZlbG9wCiAgICAgIHRlbmFudENvdW50cnk6IGdsb2JhbAogICAgYW5ub3RhdGlvbnM6CiAgICAgIHN1bW1hcnk6IENlcnRpZmljYXRlIGV4cGlyYXRpb24gaXMgdW5kZXIgMTUgZGF5cwogICAgICBkZXNjcmlwdGlvbjogQ2VydGlmaWNhdGVzIHNob3VsZCBhbHdheXMgaGF2ZSArMTUgZGF5cyBleHBpcnksIGlmIHRoZXkgZG9udCBzb21ldGhpbmcgaXMgYW1pc3MuCgogIC0gYWxlcnQ6IE5vdFJlYWR5CiAgICBmb3I6IDEwbQogICAgZXhwcjogc3VtKGNlcnRtYW5hZ2VyX2NlcnRpZmljYXRlX3JlYWR5X3N0YXR1c3tjb25kaXRpb24hPSJUcnVlIn0pIGJ5IChjb25kaXRpb24pID4gMAogICAgbGFiZWxzOgogICAgICBzZXZlcml0eTogZXJyb3IKICAgICAgZXZlbnRfdHlwZTogYXZhaWxhYmlsaXR5CiAgICAgIHRlYW06IHBsYXRmb3JtCiAgICAgIGVudjogZGV2ZWxvcAogICAgICB2ZXJzaW9uOiBtYWluCiAgICAgIHNlcnZpY2U6IGNlcnRtYW5hZ2VyCiAgICAgIHRlbmFudE5hbWU6IGRldmVsb3AKICAgICAgdGVuYW50Q291bnRyeTogZ2xvYmFsCiAgICBhbm5vdGF0aW9uczoKICAgICAgc3VtbWFyeTogQ2VydGlmaWNhdGUgaXMgbm90IHJlYWR5CiAgICAgIGRlc2NyaXB0aW9uOiBBZnRlciAxMG0sIGV2ZW4gYSBMZXRzRW5jcnlwdCBjZXJ0IHNob3VsZCBiZSB1cC4KCi0gbmFtZTogQXV0b3NjYWxpbmcKICBydWxlczoKICAtIGFsZXJ0OiBQb2RzTm90UnVubmluZ09yU3VjY2VlZGVkCiAgICBmb3I6IDE1bQogICAgZXhwcjogc3VtKGt1YmVfcG9kX3N0YXR1c19waGFzZXtwaGFzZSF+IlJ1bm5pbmd8U3VjY2VlZGVkIn0pYnkocG9kLHBoYXNlKSA+IDAKICAgIGxhYmVsczoKICAgICAgc2V2ZXJpdHk6IGVycm9yCiAgICAgIGV2ZW50X3R5cGU6IGF2YWlsYWJpbGl0eQogICAgICB0ZWFtOiBwbGF0Zm9ybQogICAgICBlbnY6IGRldmVsb3AKICAgICAgdmVyc2lvbjogbWFpbgogICAgICBzZXJ2aWNlOiBrYXJwZW50ZXIKICAgICAgdGVuYW50TmFtZTogZGV2ZWxvcAogICAgICB0ZW5hbnRDb3VudHJ5OiBnbG9iYWwKICAgIGFubm90YXRpb25zOgogICAgICBzdW1tYXJ5OiBUaGVyZSBhcmUgcG9kcyBpbiBhIG5vdCBzdWNjZXNzIHN0YXRlCiAgICAgIGRlc2NyaXB0aW9uOiBQb2QgbWlnaHQgbm90IGJlIHNjaGVkdWxhYmxlIGJlY2F1c2Ugb2YgYSBtaXNjb25maWd1cmF0aW9uIG9yIGF1dG9zY2FsaW5nIGlzc3VlcywgcG9kIG1pZ2h0IGJlIG1hcmtlZCBhcyBmYWlsZWQuCgogIC0gYWxlcnQ6IEtFREFTY2FsZWRPYmplY3RFcnJvcnMKICAgIGZvcjogNW0KICAgIGV4cHI6IHN1bShpbmNyZWFzZShrZWRhX3NjYWxlcl9lcnJvcnNfdG90YWxbMm1dKSlieShzY2FsZWRPYmplY3QsIGV4cG9ydGVkX25hbWVzcGFjZSkgPiAwCiAgICBsYWJlbHM6CiAgICAgIHNldmVyaXR5OiBlcnJvcgogICAgICBldmVudF90eXBlOiBhdmFpbGFiaWxpdHkKICAgICAgdGVhbTogcGxhdGZvcm0KICAgICAgZW52OiBkZXZlbG9wCiAgICAgIHZlcnNpb246IG1haW4KICAgICAgc2VydmljZToga2VkYQogICAgICB0ZW5hbnROYW1lOiBkZXZlbG9wCiAgICAgIHRlbmFudENvdW50cnk6IGdsb2JhbAogICAgYW5ub3RhdGlvbnM6CiAgICAgIHN1bW1hcnk6IEtFREEgaXMgaGF2aW5nIGlzc3VlcyB3aXRoIGEgU2NhbGVkT2JqZWN0CiAgICAgIGRlc2NyaXB0aW9uOiBDaGVjayBmb3IgbWlzY29uZmlndXJhdGlvbi4KCi0gbmFtZTogQ2x1c3RlckhlYWx0aAogIHJ1bGVzOgogIC0gYWxlcnQ6IE5vZGVQcmVzc3VyZQogICAgZm9yOiAxMG0KICAgIGV4cHI6IHN1bShrdWJlX25vZGVfc3RhdHVzX2NvbmRpdGlvbntzdGF0dXM9InRydWUiLCBjb25kaXRpb249fiIuKlByZXNzdXJlIn0pYnkoY29uZGl0aW9uLG5vZGUpID4gMAogICAgbGFiZWxzOgogICAgICBzZXZlcml0eTogZXJyb3IKICAgICAgZXZlbnRfdHlwZTogYXZhaWxhYmlsaXR5CiAgICAgIHRlYW06IHBsYXRmb3JtCiAgICAgIGVudjogZGV2ZWxvcAogICAgICB2ZXJzaW9uOiBtYWluCiAgICAgIHNlcnZpY2U6IGt1YmVybmV0ZXMKICAgICAgdGVuYW50TmFtZTogZGV2ZWxvcAogICAgICB0ZW5hbnRDb3VudHJ5OiBnbG9iYWwKICAgIGFubm90YXRpb25zOgogICAgICBzdW1tYXJ5OiBBIG5vZGUgaXMgcG9zdGluZyBhIHByZXNzdXJlIHN0YXR1cwogICAgICBkZXNjcmlwdGlvbjogVGhpcyBjb3VsZCBiZSBtZW1vcnksIHBpZCBvciBwcm9iYWJseSBkaXNrLiBDaGVjayBpZiByZXNvdXJjZSBhbGxvY2F0aW9uIGlzIHJpZ2h0CgogIC0gYWxlcnQ6IE5vZGVOb3RSZWFkeQogICAgZm9yOiAxMG0KICAgIGV4cHI6IHN1bShrdWJlX25vZGVfc3RhdHVzX2NvbmRpdGlvbntzdGF0dXM9ImZhbHNlIiwgY29uZGl0aW9uPSJSZWFkeSJ9KWJ5KGNvbmRpdGlvbiwgbm9kZSkgPiAwCiAgICBsYWJlbHM6CiAgICAgIHNldmVyaXR5OiBlcnJvcgogICAgICBldmVudF90eXBlOiBhdmFpbGFiaWxpdHkKICAgICAgdGVhbTogcGxhdGZvcm0KICAgICAgZW52OiBkZXZlbG9wCiAgICAgIHZlcnNpb246IG1haW4KICAgICAgc2VydmljZToga3ViZXJuZXRlcwogICAgICB0ZW5hbnROYW1lOiBkZXZlbG9wCiAgICAgIHRlbmFudENvdW50cnk6IGdsb2JhbAogICAgYW5ub3RhdGlvbnM6CiAgICAgIHN1bW1hcnk6IEEgbm9kZSBoYXMgYmVlbiBpbiBhIG5vdC1yZWFkeSBzdGF0ZSBmb3IgYSB3aGlsZQogICAgICBkZXNjcmlwdGlvbjogQ2hlY2sgaWYgaXQgaGFzIGFueSBjdXN0b21pemF0aW9ucyB0aGF0IGJyb2tlIGt1YmVsZXQgb3IgcHJlc3N1cmVzIG9mIGFueSBraW5kCgogIC0gYWxlcnQ6IEdQVUVycm9ycwogICAgZm9yOiA1bQogICAgZXhwcjogc3VtKERDR01fRklfREVWX1hJRF9FUlJPUlMpID4gMAogICAgbGFiZWxzOgogICAgICBzZXZlcml0eTogZXJyb3IKICAgICAgZXZlbnRfdHlwZTogYXZhaWxhYmlsaXR5CiAgICAgIHRlYW06IHBsYXRmb3JtCiAgICAgIGVudjogZGV2ZWxvcAogICAgICB2ZXJzaW9uOiBtYWluCiAgICAgIHNlcnZpY2U6IG52aWRpYQogICAgICB0ZW5hbnROYW1lOiBkZXZlbG9wCiAgICAgIHRlbmFudENvdW50cnk6IGdsb2JhbAogICAgYW5ub3RhdGlvbnM6CiAgICAgIHN1bW1hcnk6IEEgbm9kZSBpcyBleHBlcmllbmNpbmcgR1BVIGVycm9ycwogICAgICBkZXNjcmlwdGlvbjogVGhpcyBjb3VsZCBiZSBhIEdQVSByZXNldCBvciBoYXJkd2FyZSBmYWlsdXJlLgoKICAtIGFsZXJ0OiBQVkNVc2FnZUhpZ2gKICAgIGZvcjogNW0KICAgIGV4cHI6IHN1bShrdWJlbGV0X3ZvbHVtZV9zdGF0c191c2VkX2J5dGVzKSBieSAocGVyc2lzdGVudHZvbHVtZWNsYWltKSAvIHN1bShrdWJlbGV0X3ZvbHVtZV9zdGF0c19jYXBhY2l0eV9ieXRlcykgYnkgKHBlcnNpc3RlbnR2b2x1bWVjbGFpbSkgPiAwLjgKICAgIGxhYmVsczoKICAgICAgc2V2ZXJpdHk6IGVycm9yCiAgICAgIGV2ZW50X3R5cGU6IGF2YWlsYWJpbGl0eQogICAgICB0ZWFtOiBwbGF0Zm9ybQogICAgICBlbnY6IGRldmVsb3AKICAgICAgdmVyc2lvbjogbWFpbgogICAgICBzZXJ2aWNlOiBrdWJlcm5ldGVzCiAgICAgIHRlbmFudE5hbWU6IGRldmVsb3AKICAgICAgdGVuYW50Q291bnRyeTogZ2xvYmFsCiAgICBhbm5vdGF0aW9uczoKICAgICAgc3VtbWFyeTogQSBQVkMgaXMgb3ZlciB0aGUgODAlIHVzYWdlIG1hcmsKICAgICAgZGVzY3JpcHRpb246IEV2YWx1YXRlIGdpdmluZyBtb3JlIGhlYWRyb29tIHRvIGF2b2lkIGFwcCBicmVha2FnZSBkdWUgdG8gbGFjayBvZiBzdG9yYWdlCgogIC0gYWxlcnQ6IEhpZ2hMb2dQcmVzc3VyZQogICAgZm9yOiAxMG0KICAgIGV4cHI6IHN1bShpbmNyZWFzZShvdGVsY29sX2V4cG9ydGVyX3NlbnRfbG9nX3JlY29yZHNfdG90YWx7ZXhwb3J0ZXI9fiJhd3NjbG91ZHdhdGNobG9ncy4qIn1bMW1dKSlieShqb2IpID4gNDAwMAogICAgbGFiZWxzOgogICAgICBzZXZlcml0eTogZXJyb3IKICAgICAgZXZlbnRfdHlwZTogZmlub3BzCiAgICAgIHRlYW06IHBsYXRmb3JtCiAgICAgIGVudjogZGV2ZWxvcAogICAgICB2ZXJzaW9uOiBtYWluCiAgICAgIHNlcnZpY2U6IG9wZW50ZWxlbWV0cnkKICAgICAgdGVuYW50TmFtZTogZGV2ZWxvcAogICAgICB0ZW5hbnRDb3VudHJ5OiBnbG9iYWwKICAgIGFubm90YXRpb25zOgogICAgICBzdW1tYXJ5OiBMb2cgcmVjb3JkcyBzZW50IG92ZXIgNEsgbWFyayBpbiBhIG1pbnV0ZQogICAgICBkZXNjcmlwdGlvbjoK", "modifiedAt": "2025-03-06T13:14:28.452000-03:00", "name": "platform-monitors-global", "status": {"statusCode": "ACTIVE", "statusReason": ""}, "tags": {"services.k8s.aws/controller-version": "prometheusservice-1.2.18", "services.k8s.aws/namespace": "o11y"}}}