{"ruleGroupsNamespace": {"arn": "arn:aws:aps:us-east-1:533267304535:rulegroupsnamespace/ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf/platform-monitors-n5-settings-be", "createdAt": "2025-05-12T12:01:39.794000-03:00", "data": "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", "modifiedAt": "2025-05-12T12:02:32.913000-03:00", "name": "platform-monitors-n5-settings-be", "status": {"statusCode": "ACTIVE", "statusReason": ""}, "tags": {"services.k8s.aws/controller-version": "prometheusservice-1.2.18", "services.k8s.aws/namespace": "ccb-pan"}}}