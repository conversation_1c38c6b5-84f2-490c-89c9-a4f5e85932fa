#!/bin/bash

# Script para restaurar Amazon Managed Service for Prometheus
# Fecha del backup: 2025-07-10
# Workspace original: ws-499bb6ff-cc2f-4d5b-ad88-ef42e2e4f1bf

set -e

REGION="us-east-1"
PROFILE="ccb"
NEW_WORKSPACE_ID=""

echo "=== Restauración de Amazon Managed Service for Prometheus ==="
echo "Backup creado: $(date)"
echo ""

# Función para crear workspace
create_workspace() {
    echo "1. Creando nuevo workspace..."
    
    WORKSPACE_RESULT=$(aws amp create-workspace \
        --alias "platform-metrics-store-prod-restored" \
        --region $REGION \
        --profile $PROFILE \
        --tags Owner=Platform,Terraform=true,Env=prod,Name=platform-metrics-store-prod-restored,cost-center=CTO)
    
    NEW_WORKSPACE_ID=$(echo $WORKSPACE_RESULT | jq -r '.workspaceId')
    echo "Nuevo workspace creado: $NEW_WORKSPACE_ID"
    
    # Esperar a que esté activo
    echo "Esperando a que el workspace esté activo..."
    aws amp wait workspace-active --workspace-id $NEW_WORKSPACE_ID --region $REGION --profile $PROFILE
    echo "Workspace activo!"
}

# Función para restaurar reglas
restore_rules() {
    echo ""
    echo "2. Restaurando reglas de monitoreo..."
    
    for rule_file in rules-*.json; do
        if [ -f "$rule_file" ]; then
            namespace=$(echo $rule_file | sed 's/rules-//' | sed 's/.json//')
            echo "Restaurando namespace: $namespace"
            
            # Extraer la configuración de reglas del backup
            rule_data=$(jq -r '.ruleGroupsNamespace.data' "$rule_file")
            
            # Crear el namespace de reglas
            aws amp put-rule-groups-namespace \
                --workspace-id $NEW_WORKSPACE_ID \
                --name "$namespace" \
                --data "$rule_data" \
                --region $REGION \
                --profile $PROFILE
                
            echo "✓ Namespace $namespace restaurado"
        fi
    done
}

# Función principal
main() {
    if [ -z "$1" ]; then
        echo "Uso: $0 [create|rules-only]"
        echo ""
        echo "create     - Crear nuevo workspace y restaurar todas las reglas"
        echo "rules-only - Solo restaurar reglas (requiere workspace existente)"
        echo ""
        echo "Para rules-only, exporta NEW_WORKSPACE_ID antes de ejecutar:"
        echo "export NEW_WORKSPACE_ID=ws-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
        exit 1
    fi
    
    case $1 in
        "create")
            create_workspace
            restore_rules
            echo ""
            echo "=== Restauración completada ==="
            echo "Nuevo workspace ID: $NEW_WORKSPACE_ID"
            echo "Endpoint: https://aps-workspaces.us-east-1.amazonaws.com/workspaces/$NEW_WORKSPACE_ID/"
            ;;
        "rules-only")
            if [ -z "$NEW_WORKSPACE_ID" ]; then
                echo "Error: NEW_WORKSPACE_ID no está definido"
                echo "Exporta la variable: export NEW_WORKSPACE_ID=ws-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
                exit 1
            fi
            restore_rules
            echo ""
            echo "=== Reglas restauradas en workspace: $NEW_WORKSPACE_ID ==="
            ;;
        *)
            echo "Opción inválida: $1"
            exit 1
            ;;
    esac
}

main "$@"
