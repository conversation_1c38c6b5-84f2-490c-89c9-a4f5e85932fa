import json, logging, time, threading
from collections import deque
from decimal import Decimal
from typing import List
from src.config import settings
from src.infrastructure.aws_clients import sqs_client
from src.domain.models import UsageEvent
from src.infrastructure.usage_repository import save_batch
from src.metrics import MetricsPublisher

_log = logging.getLogger(__name__)
_sqs  = sqs_client()
_metrics = MetricsPublisher()

_STOP = threading.Event()

def stop():
    _STOP.set()

def _delete(receipt_handles: List[str]) -> None:
    entries = [
        {"Id": str(i), "ReceiptHandle": rh}
        for i, rh in enumerate(receipt_handles)
    ]
    if entries:
        _sqs.delete_message_batch(QueueUrl=settings.sqs_url, Entries=entries)

def run_consumer_loop() -> None:
    buffer: deque = deque()
    receipts: List[str] = []

    while not _STOP.is_set():
        # 1) receive
        resp = _sqs.receive_message(
            QueueUrl = settings.sqs_url,
            WaitTimeSeconds = settings.wait_seconds,
            MaxNumberOfMessages = settings.max_messages,
        )

        messages = resp.get("Messages", [])
        for msg in messages:
            parsed_body = json.loads(msg["Body"], parse_float=Decimal)
            evt = UsageEvent.from_sqs(parsed_body)
            buffer.append(evt)
            receipts.append(msg["ReceiptHandle"])

        # 2) flush when buffer ready
        while len(buffer) >= settings.batch_size:
            batch = [buffer.popleft() for _ in range(settings.batch_size)]
            save_batch(batch)
            _delete([receipts.pop(0) for _ in range(settings.batch_size)])
            _metrics.ingested(len(batch))

        # 3) occasional small flush (e.g., no new data for 5 s)
        if messages == [] and buffer:
            save_batch(list(buffer))
            _delete(receipts)
            _metrics.ingested(len(buffer))
            buffer.clear()
            receipts.clear()
