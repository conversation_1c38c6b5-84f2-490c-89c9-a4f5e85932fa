from __future__ import annotations
import os
from dataclasses import dataclass


@dataclass(frozen=True, slots=True)
class Settings:
    # ── AWS basics ────────────────────────────────────────────────────────────────
    #
    # • After you run `aws sso login --profile my-profile`, b<PERSON><PERSON> can read the
    #   cached SSO tokens automatically *if* you tell it which profile to use.
    # • If you omit the profile (None) boto3 falls back to the default chain:
    #   env-vars  ›  ~/.aws/{config,credentials}  ›  EC2/ECS metadata.
    #
    aws_profile: str | None = os.getenv("AWS_PROFILE")         # e.g.  "my-profile"
    region:      str        = (
        os.getenv("AWS_REGION")
        or os.getenv("AWS_DEFAULT_REGION")
        or "us-east-1"
    )

    # ── SQS ──────────────────────────────────────────────────────────────────────
    sqs_url:      str = os.environ["SQS_URL"]
    wait_seconds: int = int(os.getenv("SQS_WAIT_SECONDS", "20"))
    max_messages: int = int(os.getenv("SQS_MAX_MESSAGES", "10"))   # SQS hard-limit

    # ── DynamoDB ────────────────────────────────────────────────────────────────
    table_name:  str = os.environ["DDB_TABLE"]
    batch_size:  int = int(os.getenv("DDB_BATCH_SIZE", "10"))

    # ── Metrics / logging ───────────────────────────────────────────────────────
    metrics_namespace: str = os.getenv("METRICS_NAMESPACE", "UsageTrackerSvc")
    log_level:         str = os.getenv("LOG_LEVEL", "INFO")


settings = Settings()          # ← one singleton; import this everywhere
