from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Any, Mapping, Optional


@dataclass(slots=True)
class UsageEvent:
    # Core identifiers
    message_id: str
    tenant_id: str
    conversation_id: Optional[str]

    # Event metadata
    event_type: str
    provider_id: str
    action_type: str
    model_name: str

    # Timing
    timestamp: Decimal
    timestamp_iso: str

    # Usage metrics
    input_tokens: int
    output_tokens: int
    total_tokens: int

    # Audit trail
    raw: Mapping[str, Any]

    @classmethod
    def from_sqs(cls, body: Mapping[str, Any]) -> "UsageEvent":
        """Extract structured data from SQS message body"""
        usage = body.get("usage", {})
        timestamp_unix = Decimal(str(body["timestamp"]))
        timestamp_iso = datetime.fromtimestamp(float(timestamp_unix)).isoformat()

        input_tokens = usage.get("input_token_count", 0)
        output_tokens = usage.get("output_token_count", 0)

        conversation_id = usage.get("conversation_id")
        if not conversation_id or conversation_id.strip() == "":
            conversation_id = None

        return cls(
            message_id=body["message_id"],
            tenant_id=body["app_context"]["tenant_id"],
            conversation_id=conversation_id,
            event_type=body.get("type", ""),
            provider_id=body.get("provider_id", ""),
            action_type=body.get("action_type", ""),
            model_name=usage.get("model_name", ""),
            timestamp=timestamp_unix,
            timestamp_iso=timestamp_iso,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=input_tokens + output_tokens,
            raw=body
        )

    @property
    def sort_key(self) -> str:
        """Generate composite sort key for DynamoDB"""
        return f"{self.timestamp_iso}#{self.message_id}"

    @property
    def date(self) -> str:
        """Extract date for daily aggregation queries"""
        return self.timestamp_iso.split('T')[0]

    @property
    def hour(self) -> str:
        """Extract hour for hourly aggregation queries"""
        return self.timestamp_iso[:13]
