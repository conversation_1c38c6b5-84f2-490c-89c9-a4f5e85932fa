import boto3
from botocore.config import Config
from functools import lru_cache
from typing import TypeAlias, Mapping, Any
from src.config import settings

# Shared connection-pool tuning
BOTO_CFG = Config(
    retries={"max_attempts": 5, "mode": "adaptive"},
    max_pool_connections=50,
)

# Base type alias — keeps type checkers happy without extra stubs
BaseClient: TypeAlias = boto3.client  # type: ignore


@lru_cache(maxsize=1)
def _session() -> boto3.Session:
    """
    Lazily create ONE global boto3 Session.

    • Automatically picks up cached SSO tokens when `profile_name` is set to the
      same profile you used with `aws sso login`.
    • Automatically refreshes the SSO token when it expires.
    """
    return boto3.Session(
        profile_name=settings.aws_profile,
        region_name=settings.region,
    )


# ── strongly-typed helpers ──────────────────────────────────────────────────────

def sqs_client() -> BaseClient:  # SQS
    return _session().client("sqs", config=BOTO_CFG)


def dynamodb_client() -> BaseClient:  # low-level DynamoDB
    return _session().client("dynamodb", config=BOTO_CFG)


def cloudwatch_client() -> BaseClient:  # CloudWatch
    return _session().client("cloudwatch", config=BOTO_CFG)
