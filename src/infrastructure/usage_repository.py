import itertools, time, logging
from typing import Iterable
from boto3.dynamodb.types import TypeSerializer
from src.infrastructure.aws_clients import dynamodb_client
from src.config import settings
from src.domain.models import UsageEvent

_log = logging.getLogger(__name__)
_ser = TypeSerializer()
_ddb = dynamodb_client()


def _to_ddb_item_optimal(evt: UsageEvent) -> dict:
    """Convert UsageEvent to DynamoDB batch write format - OPTIMAL DESIGN"""
    ttl_seconds = int(time.time()) + (90 * 24 * 3600)  # 90 days retention

    item = {
        # Primary table keys - OPTIMAL DESIGN
        "tenant_id": _ser.serialize(evt.tenant_id),
        "timestamp_message_id": _ser.serialize(evt.sort_key),

        # Required GSI keys
        "message_id": _ser.serialize(evt.message_id),

        # Event metadata
        "event_type": _ser.serialize(evt.event_type),
        "provider_id": _ser.serialize(evt.provider_id),
        "action_type": _ser.serialize(evt.action_type),
        "model_name": _ser.serialize(evt.model_name),

        # Timing fields
        "timestamp_unix": _ser.serialize(evt.timestamp),
        "timestamp_iso": _ser.serialize(evt.timestamp_iso),
        "date": _ser.serialize(evt.date),
        "hour": _ser.serialize(evt.hour),

        # Usage metrics
        "input_tokens": _ser.serialize(evt.input_tokens),
        "output_tokens": _ser.serialize(evt.output_tokens),
        "total_tokens": _ser.serialize(evt.total_tokens),

        # Audit and lifecycle
        "payload": _ser.serialize(evt.raw),
        "ttl": _ser.serialize(ttl_seconds),
    }

    # Only include conversation_id if it exists (sparse GSI)
    if evt.conversation_id:
        item["conversation_id"] = _ser.serialize(evt.conversation_id)

    return {
        "PutRequest": {
            "Item": item
        }
    }


# Main function to use with your new optimal table
def _to_ddb_item(evt: UsageEvent) -> dict:
    """Convert UsageEvent to DynamoDB batch write format - OPTIMAL DESIGN"""
    return _to_ddb_item_optimal(evt)


def save_batch(events: Iterable[UsageEvent]) -> None:
    """Batch-write up to 25 items with retry on unprocessed_items."""
    items = [_to_ddb_item(e) for e in events]
    if not items:
        return

    request_items = {settings.table_name: items}
    while request_items:
        resp = _ddb.batch_write_item(RequestItems=request_items)
        request_items = resp.get("UnprocessedItems", {})
        if request_items:
            _log.warning("Retrying %d unprocessed items", len(request_items))
            time.sleep(0.5)
