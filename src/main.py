import logging, signal, sys, threading, time
from src.config import settings
from src.application.sqs_consumer import run_consumer_loop, stop
from src.presentation.health_server import start_health_server

logging.basicConfig(
    level=settings.log_level,
    format="%(asctime)s %(levelname)s %(threadName)s %(message)s",
)

def _graceful_shutdown(signum, frame):
    logging.info("signal %s received → shutting down", signum)
    stop()

def main():
    signal.signal(signal.SIGTERM, _graceful_shutdown)
    signal.signal(signal.SIGINT,  _graceful_shutdown)

    start_health_server()
    consumer_thread = threading.Thread(target=run_consumer_loop, name="consumer")
    consumer_thread.start()

    while consumer_thread.is_alive():
        time.sleep(1)

if __name__ == "__main__":
    sys.exit(main())
