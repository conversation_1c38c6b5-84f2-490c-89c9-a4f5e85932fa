import logging
from src.infrastructure.aws_clients import cloudwatch_client
from src.config import settings

_log = logging.getLogger(__name__)


class MetricsPublisher:
    def __init__(self) -> None:
        self._client = cloudwatch_client()
        self._template = {
            "MetricName": "MessagesIngested",
            "Unit": "Count",
            "Value": 0.0,
        }

    def ingested(self, count: int) -> None:
        try:
            metric = self._template.copy()
            metric["Value"] = float(count)

            self._client.put_metric_data(
                Namespace=settings.metrics_namespace,
                MetricData=[metric],
            )
        except Exception as exc:  # never crash pipeline on metrics
            _log.debug("metric error: %s", exc, exc_info=False)
