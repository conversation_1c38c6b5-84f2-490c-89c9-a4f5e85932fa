import logging, threading, uvicorn
from fastapi import FastAPI
from src.application import sqs_consumer

app = FastAPI()
_log = logging.getLogger(__name__)

@app.get("/healthz", summary="Liveness probe")
def health() -> dict:
    return {"status": "ok"}

@app.get("/readyz", summary="Readiness probe")
def ready() -> dict:
    # simplistic; expand with e.g. test-query DDB
    return {"ready": True}

def start_health_server() -> threading.Thread:
    server = threading.Thread(
        target=uvicorn.run,
        kwargs=dict(app="src.presentation.health_server:app",
                    host="0.0.0.0", port=8080, log_level="info"),
        daemon=True
    )
    server.start()
    return server
