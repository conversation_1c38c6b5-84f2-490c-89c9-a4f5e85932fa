{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:DeleteMessageBatch"], "Resource": ["arn:aws:sqs:us-east-1:211125382625:n5-usage-tracking-queue", "arn:aws:sqs:us-east-1:211125382625:ai-core-usage-v2"]}, {"Effect": "Allow", "Action": ["dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:GetItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem", "dynamodb:DeleteItem"], "Resource": ["arn:aws:dynamodb:us-east-1:211125382625:table/n5-usage-tracking-table", "arn:aws:dynamodb:us-east-1:211125382625:table/n5-usage-tracking-table/index/*"]}, {"Effect": "Allow", "Action": ["cloudwatch:PutMetricData"], "Resource": "*", "Condition": {"StringEquals": {"cloudwatch:namespace": "UsageTrackerSvc"}}}]}